<!-- Plik: src/App.svelte -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { invoke } from '@tauri-apps/api/core';

	let Database: any;

	onMount(async () => {
		try {
			const sqlModule: any = await import('@tauri-apps/plugin-sql');
			Database = sqlModule.Database || sqlModule.default || sqlModule.load || sqlModule;

			if (Database && typeof Database.load === 'function') {
				await initDatabase();
			} else if (typeof Database === 'function') {
				await initDatabaseDirect();
			} else {
				console.error('Could not find Database.load function');
			}

			await fetchAndRender();
		} catch (e) {
			console.error('Error in onMount:', e);
		}
	});

	const NOTE_FILE_PATH = '../notes.txt';

	interface NodeView {
		id: string;
		content: string;
		level: number;
		expanded: boolean;
		children_count: number;
	}

	let nodes = $state<NodeView[]>([]);
	// --- FIX: Change visibleNodes from $derived to $state to prevent re-rendering on text input ---
	let visibleNodes = $state<NodeView[]>([]);
	let db = $state<any>(null);
	let saveStatus = $state<'saved' | 'saving' | 'pending'>('saved');

	// --- FIX: This function is now called manually when the structure changes ---
	function updateVisibleNodes() {
		if (nodes.length === 0) {
			visibleNodes = [];
			return;
		}
		const visible: NodeView[] = [];
		const parentStack: { level: number; expanded: boolean }[] = [];
		for (const node of nodes) {
			while (parentStack.length > 0 && parentStack[parentStack.length - 1].level >= node.level) {
				parentStack.pop();
			}
			const shouldBeVisible = parentStack.every((parent) => parent.expanded);
			if (shouldBeVisible) {
				visible.push(node);
			}
			if (node.children_count > 0) {
				parentStack.push({ level: node.level, expanded: node.expanded });
			}
		}
		visibleNodes = visible;
	}

	// --- Logika bazy danych (bez zmian) ---
	async function initDatabase() {
		try {
			db = await Database.load('sqlite:test.db');
			await testConnection();
		} catch (e) {
			console.error('Database.load failed:', e);
		}
	}

	async function initDatabaseDirect() {
		try {
			db = new Database('sqlite:test.db');
			await testConnection();
		} catch (e) {
			console.error('Direct Database constructor failed:', e);
		}
	}

	async function testConnection() {
		if (!db) return;
		try {
			await db.execute(
				`CREATE TABLE IF NOT EXISTS node_states (node_id TEXT PRIMARY KEY, expanded BOOLEAN NOT NULL DEFAULT 1)`
			);
		} catch (e) {
			console.error('Database connection test failed:', e);
		}
	}

	async function loadExpandedStates() {
		if (!db) return {};
		try {
			const result: { node_id: string; expanded: boolean }[] = await db.select(
				'SELECT node_id, expanded FROM node_states'
			);
			const expandedStates: Record<string, boolean> = {};
			for (const row of result) {
				expandedStates[row.node_id] = row.expanded;
			}
			return expandedStates;
		} catch (e) {
			console.error('Error loading expanded states:', e);
			return {};
		}
	}

	async function saveExpandedState(nodeId: string, expanded: boolean) {
		if (!db) return;
		try {
			await db.execute('INSERT OR REPLACE INTO node_states (node_id, expanded) VALUES ($1, $2)', [
				nodeId,
				expanded
			]);
		} catch (e) {
			console.error('Error saving expanded state:', e);
		}
	}

	// --- Logika ładowania i renderowania ---
	async function fetchAndRender() {
		try {
			const parsedNodes = await invoke<NodeView[]>('parse_notes_file', { path: NOTE_FILE_PATH });
			const expandedStates = await loadExpandedStates();
			nodes = parsedNodes.map((node) => ({
				...node,
				expanded: expandedStates[node.id] !== undefined ? expandedStates[node.id] : true
			}));
			// --- FIX: Manually update visible nodes after loading ---
			updateVisibleNodes();
		} catch (e) {
			console.error('Błąd podczas ładowania notatek:', e);
		}
	}

	async function toggleNodeExpanded(node: NodeView) {
		// Optimistically update UI
		node.expanded = !node.expanded;
		updateVisibleNodes(); // --- FIX: Update visibility

		try {
			await saveExpandedState(node.id, node.expanded);
		} catch (e) {
			console.error('Błąd podczas zmiany stanu węzła:', e);
			// Revert on failure
			node.expanded = !node.expanded;
			updateVisibleNodes();
		}
	}

	// --- Ulepszona logika zapisu (bez zmian) ---
	let saveTimeout: ReturnType<typeof setTimeout> | null = null;

	function triggerSave() {
		if (saveTimeout) clearTimeout(saveTimeout);
		saveStatus = 'pending';
		saveTimeout = setTimeout(saveToFile, 1000);
	}

	async function saveToFile() {
		saveStatus = 'saving';
		try {
			const nodesToSave = JSON.parse(JSON.stringify(nodes));
			const fileContent = nodesToSave
				.map((node: NodeView) => {
					return ' '.repeat(node.level * 4) + node.content;
				})
				.join('\n');

			await invoke('write', { path: NOTE_FILE_PATH, contents: fileContent });
		} catch (e) {
			console.error('Error saving file:', e);
		} finally {
			saveStatus = 'saved';
		}
	}

	// --- Ulepszona nawigacja i obsługa klawiszy ---
	function handleKeyDown(e: KeyboardEvent, currentNode: NodeView) {
		const target = e.target as HTMLInputElement;

		const navigate = (direction: 'up' | 'down') => {
			// --- IMPROVEMENT: Preserve cursor column on navigation ---
			const cursorPosition = target.selectionStart ?? 0;
			const currentIndex = visibleNodes.findIndex((n) => n.id === currentNode.id);
			const nextIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

			if (nextIndex >= 0 && nextIndex < visibleNodes.length) {
				const nextNode = visibleNodes[nextIndex];
				const nextElement = document.querySelector(
					`[data-node-id="${nextNode.id}"] input`
				) as HTMLInputElement;
				if (nextElement) {
					nextElement.focus();
					setTimeout(() => {
						const newPosition = Math.min(cursorPosition, nextElement.value.length);
						nextElement.setSelectionRange(newPosition, newPosition);
					}, 0);
				}
			}
		};

		switch (e.key) {
			case 'Enter':
				e.preventDefault();
				createNewNode(currentNode);
				break;
			case 'Tab':
				e.preventDefault();
				if (e.shiftKey) {
					outdentNode(currentNode);
				} else {
					indentNode(currentNode);
				}
				break;
			case 'ArrowUp':
				e.preventDefault();
				navigate('up');
				break;
			case 'ArrowDown':
				e.preventDefault();
				navigate('down');
				break;
		}
	}

	function createNewNode(afterNode: NodeView) {
		const afterIndex = nodes.findIndex((n) => n.id === afterNode.id);
		if (afterIndex === -1) return;

		const newNode: NodeView = {
			id: `new_${Date.now()}`,
			content: '',
			level: afterNode.level,
			expanded: true,
			children_count: 0
		};

		nodes.splice(afterIndex + 1, 0, newNode);
		// --- FIX: Manually update visible nodes after creation ---
		updateVisibleNodes();

		setTimeout(() => {
			const newElement = document.querySelector(`[data-node-id="${newNode.id}"] input`);
			(newElement as HTMLInputElement)?.focus();
		}, 0);
	}

	function findNodeIndex(node: NodeView): number {
		return nodes.findIndex((n) => n.id === node.id);
	}

	function indentNode(node: NodeView) {
		const index = findNodeIndex(node);
		if (index > 0 && node.level <= nodes[index - 1].level && node.level < 10) {
			node.level++;
			// --- FIX: Manually update visible nodes after indent ---
			updateVisibleNodes();
			triggerSave();
		}
	}

	function outdentNode(node: NodeView) {
		if (node.level > 0) {
			node.level--;
			// --- FIX: Manually update visible nodes after outdent ---
			updateVisibleNodes();
			triggerSave();
		}
	}
</script>

<main class="flex flex-col min-h-screen p-4 bg-slate-50 dark:bg-slate-900">
	<h1 class="text-2xl font-bold mb-4 text-slate-800 dark:text-slate-200">Notatnik</h1>

	<div class="p-4 border rounded-lg bg-white dark:bg-slate-800 shadow-sm">
		{#if nodes.length === 0}
			<p class="text-slate-500">Brak notatek do wyświetlenia.</p>
		{:else}
			<div class="space-y-1">
				{#each visibleNodes as node (node.id)}
					<div
						data-node-id={node.id}
						style="padding-left: {node.level * 24}px;"
						class="group flex items-center py-1 px-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
					>
						<button
							onclick={() => toggleNodeExpanded(node)}
							class="flex-shrink-0 w-5 h-5 mr-2 flex items-center justify-center text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
							title={node.expanded ? 'Zwiń' : 'Rozwiń'}
						>
							{#if node.children_count > 0}
								{node.expanded ? '▼' : '►'}
							{:else}
								<span class="text-xs">•</span>
							{/if}
						</button>

						<input
							type="text"
							bind:value={node.content}
							oninput={triggerSave}
							onkeydown={(e) => handleKeyDown(e, node)}
							class="flex-1 bg-transparent border-none outline-none text-slate-800 dark:text-slate-200 font-mono text-sm focus:bg-white dark:focus:bg-slate-700 px-2 py-1 rounded transition-colors"
							placeholder="Wpisz coś..."
						/>

						{#if node.children_count > 0}
							<span
								class="ml-2 px-2 py-0.5 bg-slate-200 dark:bg-slate-600 text-slate-600 dark:text-slate-300 text-xs rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
							>
								{node.children_count}
							</span>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<footer class="mt-4 text-sm text-slate-500 flex items-center space-x-4">
		<span>{visibleNodes.length} z {nodes.length} węzłów widocznych</span>
		{#if db}
			<span class="text-green-600">• Baza danych połączona</span>
		{:else}
			<span class="text-red-600">• Baza danych rozłączona</span>
		{/if}

		<div class="w-32 text-left">
			{#if saveStatus === 'pending'}
				<span class="text-amber-600">Oczekuje...</span>
			{:else if saveStatus === 'saving'}
				<span class="text-blue-600">Zapisywanie...</span>
			{:else}
				<span class="text-green-600">Zapisano</span>
			{/if}
		</div>
	</footer>
</main>