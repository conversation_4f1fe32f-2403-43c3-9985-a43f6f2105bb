mod commands;
use commands::default::{read, write, parse_notes_file, update_node_content};
use tauri_plugin_sql::{Migration, MigrationKind};

#[allow(clippy::missing_panics_doc)]
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let migrations = vec![
        Migration {
            version: 1,
            description: "create_initial_tables",
            sql: "
                CREATE TABLE IF NOT EXISTS contents (
                    hash TEXT PRIMARY KEY,
                    content TEXT NOT NULL
                );

                CREATE TABLE IF NOT EXISTS nodes (
                    id TEXT PRIMARY KEY,
                    parentId TEXT,
                    idx INTEGER NOT NULL,
                    expanded BOOLEAN NOT NULL DEFAULT 1,
                    contentHash TEXT NOT NULL,
                    FOREIGN KEY(contentHash) REFERENCES contents(hash)
                );

                CREATE TABLE IF NOT EXISTS node_states (
                    node_id TEXT PRIMARY KEY,
                    expanded BOOLEAN NOT NULL DEFAULT 1
                );
            ",
            kind: MigrationKind::Up,
        }
    ];

    tauri::Builder::default()
        .plugin(
            tauri_plugin_sql::Builder::default()
                .add_migrations("sqlite:test.db", migrations)
                .build(),
        )
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![read, write, parse_notes_file, update_node_content])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
