use super::errors::Error;
use std::fs;
use serde::{Serialize, Deserialize};
use sha2::{Sha256, Digest};

// Struktura danych, kt<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> w<PERSON> do frontendu
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeView {
    id: String,
    content: String,
    level: u32,
    expanded: bool,
    children_count: u32,
}

#[tauri::command]
pub fn read(path: String) -> Result<String, Error> {
    let data = fs::read(path)?;
    let string = String::from_utf8(data)?;
    Ok(string)
}

#[tauri::command]
pub fn write(path: String, contents: String) -> Result<(), Error> {
    fs::write(path, contents)?;
    Ok(())
}

fn calculate_indentation_level(line: &str) -> u32 {
    let mut level = 0;
    for char in line.chars() {
        if char == ' ' {
            level += 1;
        } else if char == '\t' {
            level += 4; // Treat tab as 4 spaces
        } else {
            break;
        }
    }
    level / 4 // Convert to logical levels (assuming 4 spaces per level)
}

fn generate_content_hash(content: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(content.as_bytes());
    format!("{:x}", hasher.finalize())
}

// Simplified parse-only version - database operations moved to JavaScript
#[tauri::command]
pub async fn parse_notes_file(path: String, _app_handle: tauri::AppHandle) -> Result<Vec<NodeView>, Error> {
    // Read and parse the notes.txt file
    let current_dir = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
    let full_path = current_dir.join(&path);
    
    let Ok(content) = fs::read_to_string(&full_path) else {
        // File doesn't exist, return empty vector
        return Ok(vec![]);
    };

    let mut nodes = Vec::new();
    let lines: Vec<&str> = content.lines().collect();
    
    for (index, line) in lines.iter().enumerate() {
        // Skip empty lines
        if line.trim().is_empty() {
            continue;
        }
        
        let level = calculate_indentation_level(line);
        let trimmed_content = line.trim().to_string();
        let content_hash = generate_content_hash(&trimmed_content);
        
        // Generate a deterministic ID based on content and position
        let id = format!("{}_{}", &content_hash[..8], index);
        
        // Count direct children only (next level must be exactly level + 1)
        let mut children_count = 0;
        
        for next_line in lines.iter().skip(index + 1) {
            if next_line.trim().is_empty() {
                continue;
            }
            let next_level = calculate_indentation_level(next_line);
            
            if next_level == level + 1 {
                children_count += 1;
            } else if next_level <= level {
                // Back to same level or higher, stop counting children
                break;
            }
            // Skip deeper levels (grandchildren, etc.)
        }
        
        nodes.push(NodeView {
            id,
            content: trimmed_content,
            level,
            expanded: true, // Default state, will be overridden from database
            children_count,
        });
    }
    
    Ok(nodes)
}

#[tauri::command]
pub async fn update_node_content(path: String, node_id: String, new_content: String) -> Result<(), Error> {
    let current_dir = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
    let full_path = current_dir.join(&path);
    
    let Ok(content) = fs::read_to_string(&full_path) else {
        return Err(Error::Io(std::io::Error::new(std::io::ErrorKind::NotFound, "File not found")));
    };

    let lines: Vec<&str> = content.lines().collect();
    let mut updated_lines = Vec::new();
    
    for (index, line) in lines.iter().enumerate() {
        if line.trim().is_empty() {
            updated_lines.push((*line).to_string());
            continue;
        }
        
        let level = calculate_indentation_level(line);
        let trimmed_content = line.trim().to_string();
        let content_hash = generate_content_hash(&trimmed_content);
        let id = format!("{}_{}", &content_hash[..8], index);
        
        if id == node_id {
            // Found the node to update - preserve indentation
            let indentation = " ".repeat((level * 4) as usize);
            let updated_line = format!("{indentation}{new_content}");
            updated_lines.push(updated_line);
        } else {
            updated_lines.push((*line).to_string());
        }
    }
    
    let updated_content = updated_lines.join("\n");
    fs::write(&full_path, updated_content)?;
    
    Ok(())
}
