[package]
name = "app"
version = "1.0.0"
description = "A simple desktop application template combining Tauri 2 with Svelte 5 and shadcn-svelte with a basic ci/cd implemented."
authors = ["Alysonhower Veras Vieira"]
license = "MIT"
repository = "alysonhower/tauri2-svelte5-shadcn"
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.6.2", features = [] }
tauri-plugin-log = "2"
thiserror = "2.0.12"
tauri-plugin-sql = { version = "2", features = ["sqlite"] }
sha2 = "0.10.9"
uuid = { version = "1.17.0", features = ["v4"] }
