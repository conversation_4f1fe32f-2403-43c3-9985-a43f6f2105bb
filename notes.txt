Projekt: Tauri SQLite Outliner
    Backend setup
        ✓ Zainstalowano tauri-plugin-sql
        ✓ Dodano SHA2 dependency
        ✓ Stworzono moduł bazy danych
        - Implementacja logiki reconcile (TODO)
    Frontend changes
    Testowanie proddbo
        ✓ Zaktualizowano App.svelte na widok outlinera
        ✓ Dodano typy TypeScript
        - Dodanie interaktywności (TODO)
    Następne kroki t
        dazobaczmy to
Implementacja parsowania wcięć
    Obsługa stanów expanded/collapsed
    Funkcjonalność edycji in-place
    Synchronizacja z bazą danych
    Wirtualne przewijanie dla wydajności
    dddd
    d