# Changelog

## 2.0.0 (2025-05-19)

- Replace SvelteKit with Svelte for a leaner frontend architecture.
- Enable runes mode by default.
- Upgrade to Tailwind CSS 4.0 and remove legacy Tailwind configuration files.
- Modularize Tauri commands and enhance error handling mechanisms.
- Refactor "HelloWorld" example into a separate component, including new functionalities.
- Implemente new Prettier and ESLint configurations.
- Update project dependencies to their latest versions.
- Ensure `package-lock.json` is now tracked by version control.
- Switch Node.js version management from Bun to NVM.
- Update CI/CD workflows and Renovate bot configuration.

## 1.0.0 (2024-11-17)

- Implement basic ci/cd config
- Add MIT license
- Update package metadata and descriptions
