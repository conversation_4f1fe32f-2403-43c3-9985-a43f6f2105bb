name: Build

on:
  push:
    branches:
      - main
    paths:
      - 'src/**'
      - 'src-tauri/**'
      - 'tests/**'
      - 'package-lock.json'
      - 'src-tauri/Cargo.lock'
      - '.github/workflows/test-build.yml'
  pull_request:
  workflow_dispatch:

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-tauri:
    permissions:
      contents: write
    strategy:
      fail-fast: true

    runs-on: 'ubuntu-24.04'
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'
      - name: Install frontend dependencies
        run: npm ci

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable

      - name: install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Build Tauri
        run: npm run tauri build
